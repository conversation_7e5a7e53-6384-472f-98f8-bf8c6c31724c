import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:unstack/models/task.model.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/utils/app_logger.dart';
import 'package:unstack/theme/theme.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

class EditTaskPage extends StatefulWidget {
  final Task task;
  final RouteSettings routeSettings;

  const EditTaskPage({
    required this.task,
    required this.routeSettings,
    super.key,
  });

  @override
  State<EditTaskPage> createState() => _EditTaskPageState();
}

class _EditTaskPageState extends State<EditTaskPage> {
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late TaskPriority _selectedPriority;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool fromTaskDetails = false;

  @override
  void initState() {
    super.initState();

    // Pre-populate form fields with existing task data
    _titleController = TextEditingController(text: widget.task.title);
    _descriptionController =
        TextEditingController(text: widget.task.description);
    _selectedPriority = widget.task.priority;

    final routeData = widget.routeSettings.arguments as Map<String, dynamic>?;
    fromTaskDetails =
        routeData != null ? routeData['fromTaskDetails'] ?? false : false;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  void _updateTask() {
    final form = _formKey.currentState;
    if (form!.validate()) {
      // Create updated task
      final updatedTask = widget.task.copyWith(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        priority: _selectedPriority,
      );

      AppLogger.debug('Updated task: ${updatedTask.title}');
      AppLogger.debug('Updated description: ${updatedTask.description}');
      AppLogger.debug('Updated priority: ${updatedTask.priority}');

      // Navigate back with updated task data
      if (fromTaskDetails) {
        RouteUtils.pop(context, updatedTask);
      } else {
        RouteUtils.pop(context, updatedTask);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: Container(
        height: 80,
        margin: EdgeInsets.all(AppSpacing.lg),
        width: double.infinity,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: _titleController.text.trim().isNotEmpty
                ? AppColors.whiteColor
                : AppColors.whiteColor.withAlpha(50),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(48),
              ),
            ),
          ),
          onPressed: _updateTask,
          child: Text(
            "Save Changes",
            style: TextStyle(
              fontSize: 18 * ResponsiveUtils.fontScale,
              fontWeight: _titleController.text.trim().isNotEmpty
                  ? FontWeight.w600
                  : FontWeight.w600,
              color: _titleController.text.trim().isNotEmpty
                  ? AppColors.blackColor
                  : AppColors.textMuted,
            ),
          ),
        )
            .animate()
            .slideY(
              begin: 0.3,
              duration: 400.ms,
              curve: Curves.easeOut,
            )
            .fadeIn(),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsetsGeometry.all(AppSpacing.lg),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  top: AppSpacing.xxl,
                  bottom: AppSpacing.lg,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    HomeAppBarButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: CupertinoIcons.back,
                    ),
                    Text(
                      'Edit Task',
                      style: AppTextStyles.h2.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    SizedBox(width: 48), // Balance the row
                  ],
                ),
              ),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    TextFormField(
                      controller: _titleController,
                      keyboardType: TextInputType.name,
                      onChanged: (_) {
                        setState(() {});
                      },
                      validator: (value) {
                        if (value!.trim().length < 2) {
                          return 'Please enter a valid task title';
                        }
                        return value.trim().isEmpty
                            ? 'Please enter a valid task title'
                            : null;
                      },
                      cursorColor: AppColors.whiteColor,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textPrimary,
                        fontSize: 32,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 1,
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(bottom: 8),
                        hintText: "What's your task?",
                        hintStyle: AppTextStyles.h1.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w800,
                        ),
                        fillColor: Colors.transparent,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: AppColors.accentPink,
                            width: 0,
                          ),
                        ),
                        enabledBorder: InputBorder.none,
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: AppColors.textPrimary,
                            width: 5,
                          ),
                        ),
                      ),
                    )
                        .animate()
                        .slideY(
                          begin: 0.3,
                          duration: 400.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(),
                    const SizedBox(height: AppSpacing.md),
                    TextFormField(
                      controller: _descriptionController,
                      keyboardType: TextInputType.multiline,
                      cursorColor: AppColors.whiteColor,
                      style: AppTextStyles.bodyLarge.copyWith(
                        color: AppColors.textPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.w400,
                      ),
                      maxLines: null,
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.only(bottom: 20),
                        hintText:
                            'Break it down, describe steps, or write a note…',
                        hintStyle: AppTextStyles.h3.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w400,
                        ),
                        fillColor: Colors.transparent,
                        border: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: AppColors.accentPink,
                            width: 0,
                          ),
                        ),
                        enabledBorder: InputBorder.none,
                        focusedBorder: UnderlineInputBorder(
                          borderSide: BorderSide(
                            color: AppColors.textPrimary,
                            width: 5,
                          ),
                        ),
                      ),
                    )
                        .animate()
                        .slideY(
                          begin: 0.3,
                          duration: 400.ms,
                          curve: Curves.easeOut,
                        )
                        .fadeIn(),
                  ],
                ),
              ),
              const SizedBox(height: AppSpacing.md),
              Text(
                'Priority',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w500,
                ),
              )
                  .animate()
                  .slideY(
                    begin: 0.3,
                    duration: 400.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(),
              const SizedBox(height: AppSpacing.sm),
              Wrap(
                children: TaskPriority.values.map((e) {
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        _selectedPriority = e;
                      });
                    },
                    child: Container(
                      margin: const EdgeInsets.only(
                        right: AppSpacing.md,
                        bottom: AppSpacing.md,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: AppSpacing.md,
                        vertical: AppSpacing.sm,
                      ),
                      decoration: BoxDecoration(
                        color: _selectedPriority == e
                            ? e.color.withValues(alpha: 0.2)
                            : AppColors.backgroundTertiary,
                        borderRadius:
                            BorderRadius.circular(AppBorderRadius.full),
                        border: Border.all(
                          color: _selectedPriority == e
                              ? e.color.withValues(alpha: 0.5)
                              : AppColors.backgroundTertiary,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.flag,
                            size: 18,
                            color: e.color,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            e.label,
                            style: AppTextStyles.buttonMedium.copyWith(
                              color: _selectedPriority == e
                                  ? e.color
                                  : AppColors.textSecondary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              )
                  .animate()
                  .slideY(
                    begin: 0.3,
                    duration: 400.ms,
                    curve: Curves.easeOut,
                  )
                  .fadeIn(),
            ],
          ),
        ),
      ),
    );
  }
}
