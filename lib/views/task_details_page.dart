import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:unstack/models/task.model.dart';
import 'package:unstack/theme/theme.dart';
import 'package:unstack/routes/route.dart';
import 'package:unstack/widgets/glassmorphism_container.dart';
import 'package:unstack/widgets/home_app_bar_button.dart';

class TaskDetailsPage extends StatefulWidget {
  final String heroTag;
  final Task? task;

  const TaskDetailsPage({
    required this.heroTag,
    this.task,
    super.key,
  });

  @override
  State<TaskDetailsPage> createState() => _TaskDetailsPageState();
}

class _TaskDetailsPageState extends State<TaskDetailsPage>
    with TickerProviderStateMixin {
  late Task _currentTask;

  @override
  void initState() {
    super.initState();
    _currentTask = widget.task ??
        Task(
          id: 'temp',
          title: 'New Task',
          description: '',
          priority: TaskPriority.medium,
          createdAt: DateTime.now(),
        );
  }

  void _navigateToEditTask() async {
    final result = await RouteUtils.pushNamed(
      context,
      RoutePaths.editTaskPage,
      arguments: {
        'task': _currentTask,
        'fromTaskDetails': true,
      },
    );

    // Update task if changes were made
    if (result != null && result is Task) {
      setState(() {
        _currentTask = result;
      });
    }
  }

  void _deleteTask() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.surfaceCard,
        title: Text(
          'Delete Task',
          style: AppTextStyles.h3.copyWith(color: AppColors.textPrimary),
        ),
        content: Text(
          'Are you sure you want to delete this task? This action cannot be undone.',
          style:
              AppTextStyles.bodyMedium.copyWith(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppColors.textSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to tasks list
              HapticFeedback.heavyImpact();
            },
            child: Text(
              'Delete',
              style: TextStyle(color: AppColors.statusError),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.backgroundPrimary,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.all(AppSpacing.lg),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(),
                SizedBox(height: AppSpacing.xl),
                _buildTaskInfo(),
                SizedBox(height: AppSpacing.xl),
                _buildTaskMetadata(),
                SizedBox(height: AppSpacing.xl),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        HomeAppBarButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: CupertinoIcons.back,
        ),
        Text(
          'Task Details',
          style: AppTextStyles.h2.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w700,
          ),
        ),
        HomeAppBarButton(
          onPressed: _navigateToEditTask,
          icon: CupertinoIcons.pencil,
        ),
      ],
    );
  }

  Widget _buildTaskInfo() {
    return GlassmorphismContainer(
      padding: EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Task Title
          Text(
            _currentTask.title,
            style: AppTextStyles.h1.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w700,
              fontSize: 28,
            ),
          ),
          SizedBox(height: AppSpacing.md),

          // Priority Badge
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: AppSpacing.md,
              vertical: AppSpacing.sm,
            ),
            decoration: BoxDecoration(
              color: _currentTask.priority.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(AppBorderRadius.full),
              border: Border.all(
                color: _currentTask.priority.color.withValues(alpha: 0.5),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.flag,
                  size: 16,
                  color: _currentTask.priority.color,
                ),
                SizedBox(width: 4),
                Text(
                  _currentTask.priority.label,
                  style: AppTextStyles.bodySmall.copyWith(
                    color: _currentTask.priority.color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          if (_currentTask.description.isNotEmpty) ...[
            SizedBox(height: AppSpacing.lg),
            Text(
              'Description',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppSpacing.sm),
            Text(
              _currentTask.description,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                height: 1.5,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTaskMetadata() {
    return GlassmorphismContainer(
      padding: EdgeInsets.all(AppSpacing.lg),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Task Information',
            style: AppTextStyles.h3.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: AppSpacing.lg),

          // Created Date
          _buildMetadataRow(
            'Created',
            _formatDate(_currentTask.createdAt),
            CupertinoIcons.calendar,
          ),

          // Due Date (if exists)
          if (_currentTask.dueDate != null) ...[
            SizedBox(height: AppSpacing.md),
            _buildMetadataRow(
              'Due Date',
              _formatDate(_currentTask.dueDate!),
              CupertinoIcons.clock,
              isOverdue: _currentTask.isOverdue,
            ),
          ],

          // Status
          SizedBox(height: AppSpacing.md),
          _buildMetadataRow(
            'Status',
            _currentTask.isCompleted ? 'Completed' : 'In Progress',
            _currentTask.isCompleted
                ? CupertinoIcons.checkmark_circle_fill
                : CupertinoIcons.circle,
            statusColor: _currentTask.isCompleted
                ? AppColors.statusSuccess
                : AppColors.accentBlue,
          ),
        ],
      ),
    );
  }

  Widget _buildMetadataRow(
    String label,
    String value,
    IconData icon, {
    bool isOverdue = false,
    Color? statusColor,
  }) {
    final color = isOverdue
        ? AppColors.statusError
        : statusColor ?? AppColors.textSecondary;

    return Row(
      children: [
        Icon(
          icon,
          size: 18,
          color: color,
        ),
        SizedBox(width: AppSpacing.sm),
        Text(
          label,
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w500,
          ),
        ),
        Spacer(),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        // Edit Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: _navigateToEditTask,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.accentBlue,
              foregroundColor: AppColors.textInverse,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.lg),
              ),
            ),
            icon: Icon(CupertinoIcons.pencil),
            label: Text(
              'Edit Task',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),

        SizedBox(height: AppSpacing.md),

        // Delete Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: OutlinedButton.icon(
            onPressed: _deleteTask,
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.statusError,
              side: BorderSide(color: AppColors.statusError),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppBorderRadius.lg),
              ),
            ),
            icon: Icon(CupertinoIcons.delete),
            label: Text(
              'Delete Task',
              style: AppTextStyles.bodyLarge.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final taskDate = DateTime(date.year, date.month, date.day);

    if (taskDate == today) {
      return 'Today';
    } else if (taskDate == today.add(Duration(days: 1))) {
      return 'Tomorrow';
    } else if (taskDate == today.subtract(Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
